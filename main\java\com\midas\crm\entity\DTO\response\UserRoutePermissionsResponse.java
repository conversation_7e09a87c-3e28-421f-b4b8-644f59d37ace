package com.midas.crm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRoutePermissionsResponse {

    private Long userId;
    private String userRole;
    private List<UserPermissionResponse> permissions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPermissionResponse {
        private String routePath;
        private String routeName;
        private Boolean canAccess;
        private List<String> roles;
    }
}
