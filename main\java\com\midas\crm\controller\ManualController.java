package com.midas.crm.controller;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Role;
import com.midas.crm.repository.ManualRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.ManualService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.validator.ManualValidator;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("${api.route.manuales}")
public class ManualController {

    @Autowired
    private ManualService manualService;

    @Autowired
    private ManualValidator manualValidator;

    @Autowired
    private ManualRepository manualRepository;

    @GetMapping
    public ResponseEntity<GenericResponse<?>> index(@RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String column,
            @RequestParam(required = false) String order,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            // Obtener el rol del usuario autenticado
            Role userRole = userPrincipal != null && userPrincipal.getUser() != null ? userPrincipal.getUser().getRole()
                    : null;

            // Si no se puede determinar el rol, usar el método estándar
            if (userRole == null) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Listado exitoso",
                        manualService.index(search, page, size, column, order)));
            }

            // Usar el método que considera el rol del usuario
            return ResponseEntity.ok(new GenericResponse<>(1, "Listado exitoso",
                    manualService.indexByRole(search, page, size, column, order, userRole)));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al listar los manuales: " + e.getMessage(), null));
        }
    }

    @GetMapping("/all")
    public ResponseEntity<GenericResponse<List<Manual>>> listAll(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            // Obtener el rol del usuario autenticado
            Role userRole = userPrincipal != null && userPrincipal.getUser() != null ? userPrincipal.getUser().getRole()
                    : null;

            // Si no se puede determinar el rol, usar el método estándar
            if (userRole == null) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Listado completo", manualService.getAll()));
            }

            // Usar el método que considera el rol del usuario
            return ResponseEntity
                    .ok(new GenericResponse<>(1, "Listado completo", manualService.getAllByRole(userRole)));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al listar todos los manuales: " + e.getMessage(), null));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<Manual>> getById(@PathVariable int id) {
        try {
            return ResponseEntity.ok(new GenericResponse<>(1, "Manual encontrado", manualService.getById(id)));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(new GenericResponse<>(0, "Error al obtener el manual", null));
        }
    }

    @PostMapping
    public ResponseEntity<GenericResponse<?>> create(@Valid @ModelAttribute ManualDTO manualDto,
            BindingResult result,
            @RequestParam(required = false) MultipartFile file) {
        try {
            manualValidator.validate(manualDto, result);
            if (result.hasErrors()) {
                return ResponseEntity.unprocessableEntity()
                        .body(new GenericResponse<>(0, "Errores de validación", result.getAllErrors()));
            }
            return ResponseEntity.ok(new GenericResponse<>(1, "Manual creado", manualService.create(manualDto, file)));
        } catch (Exception e) {
            // Agregar el mensaje de error para depuración
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al crear el manual: " + e.getMessage(), null));
        }
    }

    /**
     * Actualiza un manual existente. Permite actualizaciones parciales donde solo
     * se modifican
     * los campos que vienen en el DTO.
     */
    @PutMapping(value = "/{id}", consumes = { "multipart/form-data", "application/x-www-form-urlencoded" })
    public ResponseEntity<GenericResponse<?>> update(@PathVariable int id,
            @ModelAttribute ManualDTO manualDto,
            BindingResult result,
            @RequestParam(required = false) MultipartFile file) {
        try {
            // Validar los campos que vienen en el DTO (validación flexible para
            // actualizaciones)
            manualValidator.validate(manualDto, result);
            if (result.hasErrors()) {
                return ResponseEntity.unprocessableEntity()
                        .body(new GenericResponse<>(0, "Errores de validación", result.getAllErrors()));
            }

            // Actualizar el manual con los campos proporcionados
            Manual updatedManual = manualService.update(id, manualDto, file);

            // Devolver respuesta exitosa
            return ResponseEntity.ok(new GenericResponse<>(1, "Manual actualizado correctamente", updatedManual));
        } catch (RuntimeException e) {
            // Manejar errores específicos
            if (e.getMessage().contains("eliminado")) {
                // Si el manual está eliminado, devolver un error 400 (Bad Request)
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(0, "No se puede actualizar un manual eliminado", null));
            } else if (e.getMessage().contains("no encontrado")) {
                // Si el manual no existe, devolver un error 404 (Not Found)
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(0, "Manual no encontrado", null));
            } else {
                // Para otros errores, registrar y devolver un error 500
                e.printStackTrace();
                return ResponseEntity.status(500)
                        .body(new GenericResponse<>(0, "Error al actualizar el manual: " + e.getMessage(), null));
            }
        } catch (Exception e) {
            // Registrar el error para depuración
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al actualizar el manual: " + e.getMessage(), null));
        }
    }

    /**
     * Actualiza un manual existente usando JSON. Permite actualizaciones parciales
     * donde solo se modifican
     * los campos que vienen en el DTO.
     */
    @PutMapping(value = "/{id}", consumes = "application/json")
    public ResponseEntity<GenericResponse<?>> updateJson(@PathVariable int id,
            @RequestBody ManualDTO manualDto,
            BindingResult result) {
        try {
            // Validar los campos que vienen en el DTO (validación flexible para
            // actualizaciones)
            manualValidator.validate(manualDto, result);
            if (result.hasErrors()) {
                return ResponseEntity.unprocessableEntity()
                        .body(new GenericResponse<>(0, "Errores de validación", result.getAllErrors()));
            }

            // Actualizar el manual con los campos proporcionados
            Manual updatedManual = manualService.update(id, manualDto, null);

            // Devolver respuesta exitosa
            return ResponseEntity.ok(new GenericResponse<>(1, "Manual actualizado correctamente", updatedManual));
        } catch (RuntimeException e) {
            // Manejar errores específicos
            if (e.getMessage().contains("eliminado")) {
                // Si el manual está eliminado, devolver un error 400 (Bad Request)
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(0, "No se puede actualizar un manual eliminado", null));
            } else if (e.getMessage().contains("no encontrado")) {
                // Si el manual no existe, devolver un error 404 (Not Found)
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(0, "Manual no encontrado", null));
            } else {
                // Para otros errores, registrar y devolver un error 500
                e.printStackTrace();
                return ResponseEntity.status(500)
                        .body(new GenericResponse<>(0, "Error al actualizar el manual: " + e.getMessage(), null));
            }
        } catch (Exception e) {
            // Registrar el error para depuración
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al actualizar el manual: " + e.getMessage(), null));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> delete(@PathVariable int id,
            @RequestParam(required = false) Long userDeleteId) {
        try {
            // Si no se proporciona userDeleteId, usar un valor por defecto (por ejemplo, 1
            // para el administrador)
            Long userId = userDeleteId != null ? userDeleteId : 1L;

            // Realizar eliminación directa (soft delete)
            boolean deleted = manualService.permanentDelete(id, userId);
            if (deleted) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Manual eliminado correctamente", null));
            } else {
                return ResponseEntity.status(404)
                        .body(new GenericResponse<>(0, "Manual no encontrado", null));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al eliminar el manual: " + e.getMessage(), null));
        }
    }

    @DeleteMapping("/permanent/{id}")
    public ResponseEntity<GenericResponse<String>> permanentDelete(@PathVariable int id,
            @RequestParam(required = false) Long userDeleteId) {
        try {
            // Si no se proporciona userDeleteId, usar un valor por defecto (por ejemplo, 1
            // para el administrador)
            Long userId = userDeleteId != null ? userDeleteId : 1L;

            boolean deleted = manualService.permanentDelete(id, userId);
            if (deleted) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Manual eliminado permanentemente", null));
            } else {
                return ResponseEntity.status(404).body(new GenericResponse<>(0,
                        "Manual no encontrado o no está inactivo. Debe marcar el manual como inactivo primero.", null));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body(
                    new GenericResponse<>(0, "Error al eliminar permanentemente el manual: " + e.getMessage(), null));
        }
    }

    @PutMapping("/restore/{id}")
    public ResponseEntity<GenericResponse<Manual>> restore(@PathVariable int id) {
        try {
            Manual manual = manualService.restore(id);
            if (manual != null) {
                return ResponseEntity.ok(new GenericResponse<>(1, "Manual restaurado", manual));
            } else {
                return ResponseEntity.status(404)
                        .body(new GenericResponse<>(0, "Manual no encontrado o ya restaurado", null));
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body(new GenericResponse<>(0, "Error al restaurar el manual", null));
        }
    }

    // Endpoints para filtrar por sede
    @GetMapping("/sede/{sedeId}")
    public ResponseEntity<GenericResponse<?>> indexBySede(@PathVariable Long sedeId,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String column,
            @RequestParam(required = false) String order) {
        try {
            return ResponseEntity.ok(new GenericResponse<>(1, "Listado por sede exitoso",
                    manualService.indexBySedeId(sedeId, search, page, size, column, order)));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al listar manuales por sede: " + e.getMessage(), null));
        }
    }

    @GetMapping("/sede/{sedeId}/all")
    public ResponseEntity<GenericResponse<List<Manual>>> listAllBySede(@PathVariable Long sedeId) {
        try {
            return ResponseEntity.ok(new GenericResponse<>(1, "Listado completo por sede",
                    manualService.getBySedeId(sedeId)));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al listar todos los manuales por sede: " + e.getMessage(),
                            null));
        }
    }

    // Endpoint para obtener manuales globales + de una sede específica
    @GetMapping("/mi-sede/{sedeId}")
    public ResponseEntity<GenericResponse<?>> indexForUserSede(@PathVariable Long sedeId,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String column,
            @RequestParam(required = false) String order) {
        try {
            return ResponseEntity.ok(new GenericResponse<>(1, "Listado para usuario de sede exitoso",
                    manualService.indexGlobalOrBySedeId(sedeId, search, page, size, column, order)));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0, "Error al listar manuales para usuario de sede: " + e.getMessage(),
                            null));
        }
    }

    @GetMapping("/mi-sede/{sedeId}/all")
    public ResponseEntity<GenericResponse<List<Manual>>> listAllForUserSede(@PathVariable Long sedeId) {
        try {
            return ResponseEntity.ok(new GenericResponse<>(1, "Listado completo para usuario de sede",
                    manualService.getGlobalOrBySedeId(sedeId)));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(new GenericResponse<>(0,
                            "Error al listar todos los manuales para usuario de sede: " + e.getMessage(), null));
        }
    }
}