package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.manual.ManualDTO;
import com.midas.crm.entity.Manual;
import com.midas.crm.entity.Sede;

import java.time.LocalDateTime;

public class ManualMapper {

    public static Manual toEntity(ManualDTO dto) {
        Manual manual = new Manual();
        manual.setNombre(dto.getNombre());
        manual.setTipo(dto.getTipo());
        manual.setArchivo(dto.getArchivo());
        manual.setHorario(dto.getHorario());
        manual.setIsActive(true);
        manual.setUserCreateId(dto.getUserAuthId());
        manual.setCreatedAt(LocalDateTime.now());
        return manual;
    }

    public static Manual toEntity(ManualDTO dto, Sede sede) {
        Manual manual = new Manual();
        manual.setNombre(dto.getNombre());
        manual.setTipo(dto.getTipo());
        manual.setArchivo(dto.getArchivo());
        manual.setHorario(dto.getHorario());
        manual.setIsActive(true);
        manual.setUserCreateId(dto.getUserAuthId());
        manual.setCreatedAt(LocalDateTime.now());
        manual.setSede(sede);
        return manual;
    }

    /**
     * Actualiza solo los campos que vienen en el DTO y no son nulos o vacíos
     * Esto permite actualizaciones parciales donde solo se modifican algunos campos
     */
    public static void updateEntity(Manual manual, ManualDTO dto) {
        // Actualizar nombre solo si no es nulo o vacío
        if (dto.getNombre() != null && !dto.getNombre().trim().isEmpty()) {
            manual.setNombre(dto.getNombre().trim());
        }

        // Actualizar tipo solo si no es nulo o vacío
        if (dto.getTipo() != null && !dto.getTipo().trim().isEmpty()) {
            manual.setTipo(dto.getTipo().trim());
        }

        // Actualizar archivo solo si no es nulo
        if (dto.getArchivo() != null) {
            manual.setArchivo(dto.getArchivo());
        }

        // Actualizar horario solo si no es nulo
        if (dto.getHorario() != null) {
            manual.setHorario(dto.getHorario());
        }

        // Actualizar isActive si viene en el DTO (puede ser true o false)
        if (dto.getIsActive() != null) {
            manual.setIsActive(dto.getIsActive());
            System.out.println("Actualizando estado del manual a: " + dto.getIsActive());
        } else if (dto.getIs_active() != null) {
            // Compatibilidad con el formato antiguo
            manual.setIsActive(dto.getIs_active());
            System.out.println("Actualizando estado del manual (formato antiguo) a: " + dto.getIs_active());
        }

        // Actualizar campos de auditoría
        if (dto.getUserAuthId() != null) {
            manual.setUserUpdateId(dto.getUserAuthId());
        }

        // Actualizar timestamp
        manual.setUpdatedAt(LocalDateTime.now());

        // Asegurarse de que isActive esté establecido si es null
        if (manual.getIsActive() == null) {
            manual.setIsActive(true);
        }
    }

    /**
     * Actualiza una entidad Manual incluyendo la sede
     */
    public static void updateEntity(Manual manual, ManualDTO dto, Sede sede) {
        updateEntity(manual, dto);
        manual.setSede(sede);
    }

    /**
     * Convierte una entidad Manual a ManualDTO
     */
    public static ManualDTO toDTO(Manual manual) {
        ManualDTO dto = new ManualDTO();
        dto.setNombre(manual.getNombre());
        dto.setTipo(manual.getTipo());
        dto.setArchivo(manual.getArchivo());
        dto.setHorario(manual.getHorario());
        dto.setIs_active(manual.getIsActive());
        dto.setUserAuthId(manual.getUserCreateId());

        // Incluir información de la sede si existe
        if (manual.getSede() != null) {
            dto.setSedeId(manual.getSede().getId());
        }

        return dto;
    }
}