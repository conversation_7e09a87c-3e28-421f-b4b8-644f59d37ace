package com.midas.crm.mapper;

import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialWithCoordinadorDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.DTO.user.UserWithCoordinadorDTO;
import com.midas.crm.entity.User;

import java.util.ArrayList;
import java.util.List;

public class ClienteResidencialWithCoordinadorMapper {

    public static ClienteResidencialWithCoordinadorDTO toDTO(ClienteResidencial cliente) {
        if (cliente == null) {
            return null;
        }

        ClienteResidencialWithCoordinadorDTO dto = new ClienteResidencialWithCoordinadorDTO();
        dto.setId(cliente.getId());
        dto.setCampania(cliente.getCampania());
        dto.setNombresApellidos(cliente.getNombresApellidos());
        dto.setNifNie(cliente.getNifNie());
        dto.setNacionalidad(cliente.getNacionalidad());
        dto.setFechaNacimiento(cliente.getFechaNacimiento());
        dto.setGenero(cliente.getGenero());
        dto.setCorreoElectronico(cliente.getCorreoElectronico());
        dto.setCuentaBancaria(cliente.getCuentaBancaria());
        dto.setPermanencia(cliente.getPermanencia());
        dto.setDireccion(cliente.getDireccion());
        dto.setTipoFibra(cliente.getTipoFibra());
        dto.setMovilContacto(cliente.getMovilContacto());
        dto.setFijoCompania(cliente.getFijoCompania());
        dto.setPlanActual(cliente.getPlanActual());
        dto.setCodigoPostal(cliente.getCodigoPostal());
        dto.setProvincia(cliente.getProvincia());
        dto.setDistrito(cliente.getDistrito());
        dto.setCiudad(cliente.getCiudad());
        dto.setTipoPlan(cliente.getTipoPlan());
        dto.setIcc(cliente.getIcc());

        // Asegurarse de que movilesAPortar esté inicializado correctamente
        if (cliente.getMovilesAPortar() != null) {
            dto.setMovilesAPortar(new ArrayList<>(cliente.getMovilesAPortar()));
        } else {
            dto.setMovilesAPortar(new ArrayList<>());
        }

        // Convertir el usuario y su coordinador
        if (cliente.getUsuario() != null) {
            UserWithCoordinadorDTO usuarioDTO = new UserWithCoordinadorDTO();
            User usuario = cliente.getUsuario();

            usuarioDTO.setId(usuario.getId());
            usuarioDTO.setUsername(usuario.getUsername());
            usuarioDTO.setPassword(usuario.getPassword());
            usuarioDTO.setNombre(usuario.getNombre());
            usuarioDTO.setApellido(usuario.getApellido());
            usuarioDTO.setDni(usuario.getDni());
            usuarioDTO.setTelefono(usuario.getTelefono());
            usuarioDTO.setEmail(usuario.getEmail());
            usuarioDTO.setFechaCreacion(usuario.getFechaCreacion());
            usuarioDTO.setFechaCese(usuario.getFechaCese());
            usuarioDTO.setEstado(usuario.getEstado());
            usuarioDTO.setRole(usuario.getRole());
            usuarioDTO.setToken(usuario.getToken());
            usuarioDTO.setDeletionTime(usuario.getDeletionTime());
            usuarioDTO.setSede(usuario.getSedeNombre());
            usuarioDTO.setTokenPassword(usuario.getTokenPassword());

            // Convertir el coordinador si existe
            if (usuario.getCoordinador() != null) {
                User coordinador = usuario.getCoordinador();
                UserDTO coordinadorDTO = new UserDTO();

                coordinadorDTO.setId(coordinador.getId());
                coordinadorDTO.setUsername(coordinador.getUsername());
                coordinadorDTO.setNombre(coordinador.getNombre());
                coordinadorDTO.setApellido(coordinador.getApellido());
                coordinadorDTO.setDni(coordinador.getDni());
                coordinadorDTO.setTelefono(coordinador.getTelefono());
                coordinadorDTO.setEmail(coordinador.getEmail());
                coordinadorDTO.setFechaCreacion(coordinador.getFechaCreacion());
                coordinadorDTO.setFechaCese(coordinador.getFechaCese());
                coordinadorDTO.setEstado(coordinador.getEstado());
                coordinadorDTO.setRole(coordinador.getRole());
                coordinadorDTO.setSede(coordinador.getSedeNombre());

                usuarioDTO.setCoordinador(coordinadorDTO);
            }

            dto.setUsuario(usuarioDTO);
        }

        dto.setAutorizaSeguros(cliente.getAutorizaSeguros());
        dto.setAutorizaEnergias(cliente.getAutorizaEnergias());
        dto.setVentaRealizada(cliente.getVentaRealizada());
        dto.setDeseaPromocionesLowi(cliente.getDeseaPromocionesLowi());
        dto.setFechaCreacion(cliente.getFechaCreacion());
        dto.setObservacion(cliente.getObservacion());
        dto.setNumeroAgente(cliente.getNumeroAgente());
        dto.setEstadoLlamada(cliente.getEstadoLlamada());
        dto.setTitularDelServicio(cliente.getTitularDelServicio());
        dto.setTipoTecnologia(cliente.getTipoTecnologia());
        dto.setVelocidad(cliente.getVelocidad());
        dto.setFutbol(cliente.getFutbol());
        dto.setNumeroMoviles(cliente.getNumeroMoviles());
        dto.setTextoTranscription(cliente.getTextoTranscription());
        dto.setUrlDriveTranscripcion(cliente.getUrlDriveTranscripcion());
        dto.setNotaAgenteComparadorIA(cliente.getNotaAgenteComparadorIA());

        return dto;
    }
}
