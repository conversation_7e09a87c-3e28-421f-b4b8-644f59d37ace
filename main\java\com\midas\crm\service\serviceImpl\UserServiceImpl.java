package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.user.DeleteUserDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.DTO.user.UserPageDTO;
import com.midas.crm.entity.DTO.user.UserUpdateDTO;
import com.midas.crm.entity.DTO.websocket.UserStatusDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.UserMapper;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtProvider;
import com.midas.crm.service.ExcelService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import com.midas.crm.utils.validation.UserValidation;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtProvider jwtProvider;
    private final ExcelService excelService;
    @Autowired
    private SedeRepository sedeRepository;
    @Autowired
    private org.springframework.context.ApplicationContext applicationContext;

    // Inicializa el usuario ADMIN si aún no existe
    private void initializeAdminUser() {
        try {
            if (!userRepository.existsByUsername("70680710") && !userRepository.existsByDni("70680710")) {
                User adminUser = new User();
                adminUser.setUsername("70680710");
                adminUser.setPassword(passwordEncoder.encode("$olutions2K25."));
                adminUser.setNombre("Andree");
                adminUser.setApellido("Admin");
                adminUser.setTelefono("123456789");
                adminUser.setSedeNombre("Chiclayo");
                adminUser.setDni("70680710");
                adminUser.setEmail("<EMAIL>");
                adminUser.setFechaCreacion(LocalDateTime.now());
                adminUser.setRole(Role.ADMIN);
                adminUser.setEstado("A");
                userRepository.save(adminUser);
                log.info("Usuario ADMIN creado exitosamente.");
            } else {
                log.info("El usuario ADMIN ya existe.");
            }
        } catch (Exception e) {
            log.error("Error al inicializar el usuario ADMIN", e);
        }
    }

    @Override
    public Page<User> findAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public Page<User> findAllUsersBySede(Long sedeId, Pageable pageable) {
        if (sedeId == null) {
            return findAllUsers(pageable);
        }
        return userRepository.findAllBySede_Id(sedeId, pageable);
    }

    @Override
    public User saveUserIndividual(User user) {
        String encodedPassword = passwordEncoder.encode(user.getPassword());
        log.debug("Longitud de la contraseña cifrada: {}", encodedPassword.length());
        user.setPassword(encodedPassword);

        // Asignar rol por defecto si no tiene
        if (user.getRole() == null) {
            user.setRole(Role.ASESOR);
        }

        // Asegurar que el email no sea vacío para evitar problemas con la restricción
        // unique
        if (user.getEmail() == null || user.getEmail().isEmpty() || user.getEmail().trim().isEmpty()) {
            user.setEmail(user.getUsername() + "@midas.pe");
            log.info("Email generado automáticamente en saveUserIndividual para {}: {}", user.getUsername(),
                    user.getEmail());
        }

        user.setEstado("A");
        user.setFechaCreacion(LocalDateTime.now());
        return userRepository.save(user);
    }

    @Override
    public User saveUser(User user) {
        String encodedPassword = passwordEncoder.encode(user.getPassword());
        log.debug("Longitud de la contraseña cifrada: {}", encodedPassword.length());
        user.setPassword(encodedPassword);
        user.setRole(Role.ASESOR);

        // Asegurar que el email no sea vacío para evitar problemas con la restricción
        // unique
        if (user.getEmail() == null || user.getEmail().isEmpty() || user.getEmail().trim().isEmpty()) {
            user.setEmail(user.getUsername() + "@midas.pe");
            log.info("Email generado automáticamente en saveUser para {}: {}", user.getUsername(), user.getEmail());
        }

        user.setEstado("A");
        user.setFechaCreacion(LocalDateTime.now());
        return userRepository.save(user);
    }

    @Override
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public Optional<User> findByDni(String dni) {
        return userRepository.findByDni(dni);
    }

    @Transactional
    @Override
    public void changeRole(Role newRole, String username) {
        userRepository.updateUserRole(username, newRole);
    }

    @Override
    public User findByUsernameReturnToken(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("El usuario no existe: " + username));
        String jwt = jwtProvider.generateToken(UserPrincipal.build(user));
        user.setToken(jwt);
        return user;
    }

    @Override
    @Transactional(readOnly = true)
    public User findUserById(Long userId) {
        User user = userRepository.findById(userId).orElse(null);

        if (user != null) {
            // Crear una copia limpia del usuario para evitar problemas de lazy loading
            User userCopy = new User();
            copyUserProperties(user, userCopy);

            // Si el usuario tiene una sede, cargarla completamente
            if (user.getSede() != null) {
                try {
                    // Buscar la sede completa por ID
                    Sede sede = sedeRepository.findById(user.getSede().getId()).orElse(null);
                    if (sede != null) {
                        userCopy.setSede(sede);
                        log.info("Sede ID {} cargada completamente para el usuario {}", sede.getId(), userId);
                    }
                } catch (Exception e) {
                    log.error("Error al cargar la sede para el usuario {}: {}", userId, e.getMessage());
                }
            }

            // Si necesitamos información del coordinador, la obtenemos de forma segura
            if (user.getCoordinador() != null) {
                try {
                    User coordinador = new User(user.getCoordinador().getId());
                    // Solo copiamos propiedades básicas del coordinador si están disponibles
                    if (user.getCoordinador().getUsername() != null) {
                        coordinador.setUsername(user.getCoordinador().getUsername());
                        coordinador.setNombre(user.getCoordinador().getNombre());
                        coordinador.setApellido(user.getCoordinador().getApellido());
                    }
                    userCopy.setCoordinador(coordinador);
                    log.info("Coordinador ID {} inicializado para el usuario {}", coordinador.getId(), userId);
                } catch (Exception e) {
                    log.error("Error al inicializar coordinador para el usuario {}: {}", userId, e.getMessage());
                }
            }

            // No intentamos inicializar la colección de asesores aquí
            // ya que puede causar problemas de lazy loading
            return userCopy;
        }

        return null;
    }

    @Transactional
    @Override
    public void saveUsers(List<User> users) {
        users.forEach(user -> {
            if (userRepository.existsByUsername(user.getUsername()) || userRepository.existsByDni(user.getDni())) {
                return; // Salta si ya existe
            }
            if (user.getEmail() == null || user.getEmail().isEmpty()) {
                user.setEmail(user.getUsername() + "@midas.pe");
            }
            user.setRole(Role.ASESOR);
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            user.setEstado("A");
            user.setFechaCreacion(LocalDateTime.now());
            userRepository.save(user);
        });
    }

    @Transactional
    @Override
    public void saveUsersBackOffice(List<User> users) {
        users.forEach(user -> {
            if (userRepository.existsByUsername(user.getUsername()) || userRepository.existsByDni(user.getDni())) {
                return;
            }
            if (user.getEmail() == null || user.getEmail().isEmpty()) {
                user.setEmail(user.getUsername() + "@midas.pe");
            }
            user.setRole(Role.BACKOFFICE);
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            user.setEstado("A");
            user.setFechaCreacion(LocalDateTime.now());
            userRepository.save(user);
        });
    }

    @Override
    @Transactional
    public User updateUser(Long userId, User updateUser) {
        return userRepository.findById(userId).map(existingUser -> {
            // Verificar si se está actualizando la contraseña
            if (updateUser.getPassword() != null && !updateUser.getPassword().isEmpty()) {
                // Encriptar la contraseña antes de guardarla
                String encodedPassword = passwordEncoder.encode(updateUser.getPassword());
                existingUser.setPassword(encodedPassword);
                log.info("Contraseña actualizada y encriptada para el usuario ID: {}", userId);
            }

            // Convertir a DTO para manejar la actualización de manera uniforme
            UserUpdateDTO updateDTO = new UserUpdateDTO();
            updateDTO.setUsername(updateUser.getUsername());
            updateDTO.setNombre(updateUser.getNombre());
            updateDTO.setApellido(updateUser.getApellido());
            updateDTO.setTelefono(updateUser.getTelefono());
            updateDTO.setEmail(updateUser.getEmail());
            updateDTO.setEstado(updateUser.getEstado());
            updateDTO.setRole(updateUser.getRole()); // Agregar el rol

            // Manejar la sede: primero intentar con sede.id, luego con sede_id
            if (updateUser.getSede() != null && updateUser.getSede().getId() != null) {
                updateDTO.setSede_id(updateUser.getSede().getId());
                log.info("Actualizando sede del usuario {} a sede_id: {}",
                        existingUser.getUsername(), updateUser.getSede().getId());
            } else {
                // Intentar acceder al campo sede_id usando reflexión
                try {
                    java.lang.reflect.Field field = updateUser.getClass().getDeclaredField("sede_id");
                    field.setAccessible(true);
                    Long sedeId = (Long) field.get(updateUser);

                    if (sedeId != null) {
                        updateDTO.setSede_id(sedeId);
                        log.info("Actualizando sede del usuario {} a sede_id: {}",
                                existingUser.getUsername(), sedeId);
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // Ignorar, no hay sede_id
                }
            }

            // Actualizar el usuario usando el mapper
            UserMapper.updateUserFromDTO(existingUser, updateDTO);

            return userRepository.save(existingUser);
        }).orElse(null);
    }

    @Override
    public boolean deleteUser(Long userId) {
        if (!userRepository.existsById(userId)) {
            return false;
        }

        // Obtener el usuario para verificar su estado
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            return false;
        }

        // Si el usuario ya está inactivo, eliminarlo definitivamente
        if ("I".equals(user.getEstado())) {
            userRepository.deleteById(userId);
            log.info("Usuario con ID {} eliminado definitivamente", userId);
            return true;
        }
        // Si el usuario está activo, marcarlo como inactivo
        else if ("A".equals(user.getEstado())) {
            user.setEstado("I");
            user.setDeletionTime(LocalDateTime.now());
            userRepository.save(user);
            log.info("Usuario con ID {} marcado como inactivo", userId);
            return true;
        }

        return false;
    }

    @Override
    public boolean deleteUser(DeleteUserDTO deleteUserDTO) {
        if (!userRepository.existsById(deleteUserDTO.getUserId())) {
            return false;
        }

        // Obtener el usuario para verificar su estado
        User user = userRepository.findById(deleteUserDTO.getUserId()).orElse(null);
        if (user == null) {
            return false;
        }

        // Si se solicita eliminación permanente y el usuario ya está inactivo,
        // eliminarlo definitivamente
        if (deleteUserDTO.isPermanent() && "I".equals(user.getEstado())) {
            userRepository.deleteById(deleteUserDTO.getUserId());
            log.info("Usuario con ID {} eliminado definitivamente", deleteUserDTO.getUserId());
            return true;
        }
        // Si no se solicita eliminación permanente o el usuario está activo, marcarlo
        // como inactivo
        else if (!deleteUserDTO.isPermanent() || "A".equals(user.getEstado())) {
            user.setEstado("I");
            user.setDeletionTime(LocalDateTime.now());
            userRepository.save(user);
            log.info("Usuario con ID {} marcado como inactivo", deleteUserDTO.getUserId());
            return true;
        }

        return false;
    }

    @Override
    public Page<User> searchAllFields(String query, Pageable pageable) {
        if (query == null || query.trim().isEmpty()) {
            return userRepository.findAll(pageable);
        }
        return userRepository.searchAllFields(query, pageable);
    }

    @Override
    public Page<User> searchAllFieldsBySede(String query, Long sedeId, Pageable pageable) {
        if (sedeId == null) {
            return searchAllFields(query, pageable);
        }

        if (query == null || query.trim().isEmpty()) {
            return userRepository.findAllBySede_Id(sedeId, pageable);
        }

        return userRepository.searchAllFieldsBySede(query, sedeId, pageable);
    }

    private User updateUserAttributes(User existingUser, User updateUser) {
        if (updateUser.getNombre() != null) {
            existingUser.setNombre(updateUser.getNombre());
        }
        if (updateUser.getApellido() != null) {
            existingUser.setApellido(updateUser.getApellido());
        }
        if (updateUser.getUsername() != null) {
            existingUser.setUsername(updateUser.getUsername());
        }
        if (updateUser.getTelefono() != null) {
            existingUser.setTelefono(updateUser.getTelefono());
        }
        if (updateUser.getEmail() != null) {
            existingUser.setEmail(updateUser.getEmail());
        }
        if (updateUser.getTokenPassword() != null) {
            existingUser.setTokenPassword(updateUser.getTokenPassword());
        }
        return existingUser;
    }

    @Override
    public List<User> findAllCoordinadores() {
        // Usar el método que ordena alfabéticamente por nombre y apellido
        return userRepository.findByRoleOrderByNombreAscApellidoAsc(Role.COORDINADOR);
    }

    @Override
    public List<User> findAsesoresSinCoordinador() {
        return userRepository.findByRoleAndCoordinadorIsNull(Role.ASESOR);
    }

    // Implementación de los nuevos métodos

    @Override
    public ResponseEntity<GenericResponse<User>> registrarUsuario(User user) {
        try {
            // Validar todos los atributos del usuario
            List<String> validationErrors = UserValidation.validateUser(user);
            if (!validationErrors.isEmpty()) {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                MidasErrorMessage.USUARIO_INVALID_DATA.getErrorMessage(),
                                user));
            }

            // Validar si el usuario ya existe por username o DNI
            if (userRepository.existsByUsername(user.getUsername())) {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                MidasErrorMessage.USUARIO_USERNAME_EXISTS.getErrorMessage(),
                                user));
            }

            if (userRepository.existsByDni(user.getDni())) {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                MidasErrorMessage.USUARIO_DNI_EXISTS.getErrorMessage(),
                                user));
            }

            // Manejar el caso de email vacío
            if (user.getEmail() == null || user.getEmail().isEmpty() || user.getEmail().trim().isEmpty()) {
                user.setEmail(user.getUsername() + "@midas.pe");
                log.info("Email generado automáticamente para el usuario {}: {}", user.getUsername(), user.getEmail());
            } else if (userRepository.findByEmail(user.getEmail()).isPresent()) {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                MidasErrorMessage.USUARIO_ALREADY_EXISTS.getErrorMessage(),
                                user));
            }

            // Llama a saveUserIndividual, que solo guarda el usuario sin manipular token o
            // sesión
            User usuarioRegistrado = saveUserIndividual(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Usuario registrado exitosamente",
                            usuarioRegistrado));
        } catch (Exception e) {
            log.error("Error al registrar usuario: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            MidasErrorMessage.ERROR_INTERNAL.getErrorMessage(),
                            user));
        }
    }

    @Override
    public ResponseEntity<GenericResponse<String>> createBackofficeUsersFromExcel(MultipartFile file) {
        if (file.isEmpty()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_INVALID_DATA);
        }

        try {
            List<User> users = excelService.leerUsuariosDesdeExcelBackoffice(file);
            saveUsersBackOffice(users);
            return ResponseEntity.status(HttpStatus.CREATED).body(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Usuarios BACKOFFICE cargados exitosamente",
                            "Proceso completado correctamente"));
        } catch (IOException e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public ResponseEntity<GenericResponse<String>> createUsersFromExcel(MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            MidasErrorMessage.USUARIO_INVALID_DATA.getErrorMessage(),
                            null));
        }

        try {
            List<User> users = excelService.leerUsuariosDesdeExcel(file, Role.ASESOR);
            saveUsers(users);
            return ResponseEntity.status(HttpStatus.CREATED).body(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Usuarios cargados exitosamente",
                            "Proceso completado correctamente"));
        } catch (IOException e) {
            return ResponseEntity.internalServerError().body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            MidasErrorMessage.ERROR_INTERNAL.getErrorMessage(),
                            null));
        }
    }

    @Override
    public ResponseEntity<GenericResponse<UserPageDTO>> listUsers(int page, int size, Long sedeId) {
        try {
            Pageable paging = PageRequest.of(page, size);
            Page<User> pageUsers;

            // Usar las consultas optimizadas con ordenación alfabética
            if (sedeId != null) {
                log.info("Filtrando usuarios por sede_id: {} ordenados alfabéticamente", sedeId);
                return listUsersBySede(sedeId, page, size);
            } else {
                log.info("Listando todos los usuarios ordenados alfabéticamente, página: {}, tamaño: {}", page, size);
                // Usar el método que ordena alfabéticamente
                pageUsers = userRepository.findAllOrderedByNombreApellido(paging);
            }

            // Obtener IDs de los usuarios para cargar relaciones en una sola consulta
            List<Long> userIds = pageUsers.getContent().stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            // Cargar usuarios con sus relaciones en una sola consulta
            List<User> usersWithRelations = userRepository.findAllWithSedeAndCoordinadorByIds(userIds);

            // Mapear por ID para mantener el orden original
            Map<Long, User> userMap = usersWithRelations.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));

            // Reconstruir la lista en el orden original
            List<User> orderedUsers = userIds.stream()
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Convertir a DTOs
            List<UserDTO> userDTOList = orderedUsers.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());

            // Construir objeto de respuesta
            UserPageDTO userPageDTO = new UserPageDTO(
                    userDTOList,
                    pageUsers.getNumber(),
                    pageUsers.getTotalElements(),
                    pageUsers.getTotalPages());

            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Lista de usuarios obtenida exitosamente",
                            userPageDTO));
        } catch (Exception e) {
            log.error("Error al listar usuarios: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public ResponseEntity<GenericResponse<UserPageDTO>> listUsers(int page, int size, Long sedeId,
            boolean soloConectados) {
        try {
            Pageable paging = PageRequest.of(page, size);
            Page<User> pageUsers;

            // Si se solicita solo usuarios conectados, usar método especializado
            if (soloConectados) {
                return listConnectedUsers(page, size, sedeId);
            }

            // Usar las consultas optimizadas con ordenación alfabética
            if (sedeId != null) {
                log.info("Filtrando usuarios por sede_id: {} ordenados alfabéticamente", sedeId);
                return listUsersBySede(sedeId, page, size);
            } else {
                log.info("Listando todos los usuarios ordenados alfabéticamente, página: {}, tamaño: {}", page, size);
                // Usar el método que ordena alfabéticamente
                pageUsers = userRepository.findAllOrderedByNombreApellido(paging);
            }

            // Obtener IDs de los usuarios para cargar relaciones en una sola consulta
            List<Long> userIds = pageUsers.getContent().stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            // Cargar usuarios con sus relaciones en una sola consulta
            List<User> usersWithRelations = userRepository.findAllWithSedeAndCoordinadorByIds(userIds);

            // Mapear por ID para mantener el orden original
            Map<Long, User> userMap = usersWithRelations.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));

            // Reconstruir la lista en el orden original
            List<User> orderedUsers = userIds.stream()
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Convertir a DTOs
            List<UserDTO> userDTOList = orderedUsers.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());

            // Construir objeto de respuesta
            UserPageDTO userPageDTO = new UserPageDTO(
                    userDTOList,
                    pageUsers.getNumber(),
                    pageUsers.getTotalElements(), // Total items real de la base de datos
                    pageUsers.getTotalPages()); // Total pages real de la base de datos

            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            soloConectados ? "Lista de usuarios conectados obtenida exitosamente"
                                    : "Lista de usuarios obtenida exitosamente",
                            userPageDTO));
        } catch (Exception e) {
            log.error("Error al listar usuarios: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    /**
     * Lista solo los usuarios que están conectados actualmente
     */
    private ResponseEntity<GenericResponse<UserPageDTO>> listConnectedUsers(int page, int size, Long sedeId) {
        try {
            // Obtener todos los usuarios primero (sin paginación para filtrar por conexión)
            List<User> allUsers;
            if (sedeId != null) {
                // Usar una paginación grande para obtener todos los usuarios de la sede
                Pageable largePaging = PageRequest.of(0, 10000);
                Page<User> pageUsers = userRepository.findAllBySede_IdOrderedByNombreApellido(sedeId, largePaging);
                allUsers = pageUsers.getContent();
            } else {
                // Usar una paginación grande para obtener todos los usuarios
                Pageable largePaging = PageRequest.of(0, 10000);
                Page<User> pageUsers = userRepository.findAllOrderedByNombreApellido(largePaging);
                allUsers = pageUsers.getContent();
            }

            // Filtrar solo usuarios conectados usando el ApplicationContext para evitar
            // dependencia circular
            List<User> connectedUsers = allUsers.stream()
                    .filter(user -> {
                        try {
                            // Obtener el UserConnectionService del contexto de Spring
                            com.midas.crm.service.UserConnectionService connectionService = applicationContext
                                    .getBean(com.midas.crm.service.UserConnectionService.class);
                            return connectionService.isUserConnected(user.getId());
                        } catch (Exception e) {
                            // Si hay error al obtener el servicio o verificar conexión, incluir el usuario
                            log.debug("Error al verificar conexión del usuario {}: {}", user.getId(), e.getMessage());
                            return false;
                        }
                    })
                    .collect(Collectors.toList());

            // Aplicar paginación manual a la lista filtrada
            int start = page * size;
            int end = Math.min(start + size, connectedUsers.size());

            List<User> paginatedUsers = start < connectedUsers.size() ? connectedUsers.subList(start, end)
                    : new ArrayList<>();

            // Convertir a DTOs
            List<UserDTO> userDTOList = paginatedUsers.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());

            // Calcular información de paginación
            long totalElements = connectedUsers.size();
            int totalPages = (int) Math.ceil((double) totalElements / size);

            // Construir objeto de respuesta
            UserPageDTO userPageDTO = new UserPageDTO(
                    userDTOList,
                    page,
                    totalElements,
                    totalPages);

            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Lista de usuarios conectados obtenida exitosamente",
                            userPageDTO));
        } catch (Exception e) {
            log.error("Error al listar usuarios conectados: {}", e.getMessage(), e);
            // En caso de error, devolver lista vacía
            UserPageDTO emptyPage = new UserPageDTO(new ArrayList<>(), page, 0L, 0);
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "No se pudieron obtener usuarios conectados",
                            emptyPage));
        }
    }

    /**
     * Método auxiliar para listar usuarios por sede sin caché
     */
    private ResponseEntity<GenericResponse<UserPageDTO>> listUsersBySede(Long sedeId, int page, int size) {
        try {
            Pageable paging = PageRequest.of(page, size);
            // Usar el método que ordena alfabéticamente por sede
            Page<User> pageUsers = userRepository.findAllBySede_IdOrderedByNombreApellido(sedeId, paging);

            // Obtener IDs de los usuarios para cargar relaciones en una sola consulta
            List<Long> userIds = pageUsers.getContent().stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            // Cargar usuarios con sus relaciones en una sola consulta
            List<User> usersWithRelations = userRepository.findAllWithSedeAndCoordinadorByIds(userIds);

            // Mapear por ID para mantener el orden original
            Map<Long, User> userMap = usersWithRelations.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));

            // Construir lista de DTOs manteniendo el orden original
            List<UserDTO> userDTOList = pageUsers.getContent().stream()
                    .map(user -> {
                        User userWithRelations = userMap.get(user.getId());
                        return userWithRelations != null ? UserMapper.toDTO(userWithRelations) : UserMapper.toDTO(user);
                    })
                    .collect(Collectors.toList());

            // Construir objeto de respuesta
            UserPageDTO userPageDTO = new UserPageDTO(
                    userDTOList,
                    pageUsers.getNumber(),
                    pageUsers.getTotalElements(),
                    pageUsers.getTotalPages());

            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Lista de usuarios obtenida exitosamente",
                            userPageDTO));
        } catch (Exception e) {
            log.error("Error al listar usuarios por sede: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public ResponseEntity<GenericResponse<Map<String, Object>>> getCurrentUser(Object userPrincipal,
            HttpServletRequest request) {
        UserPrincipal principal = (UserPrincipal) userPrincipal;

        if (principal == null || principal.getUsername() == null) {
            log.warn("Intento de acceso sin autenticación.");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "No autorizado", null));
        }

        log.info("Obteniendo información del usuario autenticado: {}", principal.getUsername());

        // Obtenemos el usuario real desde base de datos
        User user = findByUsername(principal.getUsername())
                .orElse(null);

        if (user == null) {
            log.warn("Usuario no encontrado: {}", principal.getUsername());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Usuario no encontrado", null));
        }

        Map<String, Object> responseData = new HashMap<>();

        // Analiza el token original para ver si debe renovarse
        Claims claims = jwtProvider.extractClaims(request);
        if (claims != null) {
            Date expiration = claims.getExpiration();
            long tiempoRestante = expiration.getTime() - System.currentTimeMillis();
            long umbralRenovacion = 86400000L; // 24 horas en ms

            if (tiempoRestante < umbralRenovacion) {
                String nuevoToken = jwtProvider.generateToken(UserPrincipal.build(user));
                user.setToken(nuevoToken); // Asignar el token al usuario
                log.info("🔄 Token renovado automáticamente para el usuario {}", user.getUsername());
            }
        }

        // Armamos la respuesta según el rol
        if (user.getRole() == Role.COORDINADOR) {
            List<Map<String, Object>> asesoresData = new ArrayList<>();
            if (user.getAsesores() != null) {
                for (User asesor : user.getAsesores()) {
                    Map<String, Object> asesorInfo = new HashMap<>();
                    asesorInfo.put("id", asesor.getId());
                    asesorInfo.put("username", asesor.getUsername());
                    asesorInfo.put("nombre", asesor.getNombre());
                    asesorInfo.put("apellido", asesor.getApellido());
                    asesoresData.add(asesorInfo);
                }
            }
            // Evitar problemas de serialización
            User userCopy = new User();
            copyUserProperties(user, userCopy);
            userCopy.setAsesores(null);

            responseData.put("user", userCopy);
            responseData.put("asesores", asesoresData);
        } else if (user.getRole() == Role.ASESOR) {
            // Evitar problemas de serialización
            User userCopy = new User();
            copyUserProperties(user, userCopy);
            userCopy.setCoordinador(null);

            responseData.put("user", userCopy);

            if (user.getCoordinador() != null) {
                User coord = user.getCoordinador();
                Map<String, Object> coordData = new HashMap<>();
                coordData.put("id", coord.getId());
                coordData.put("username", coord.getUsername());
                coordData.put("nombre", coord.getNombre());
                coordData.put("apellido", coord.getApellido());
                responseData.put("coordinador", coordData);
            } else {
                responseData.put("coordinador", null);
            }
        } else {
            // Evitar problemas de serialización
            User userCopy = new User();
            copyUserProperties(user, userCopy);
            responseData.put("user", userCopy);
        }

        log.info("✅ Usuario actual obtenido correctamente: {}", user.getUsername());

        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Usuario actual obtenido exitosamente",
                        responseData));
    }

    /**
     * Método auxiliar para copiar propiedades de un usuario a otro
     */
    private void copyUserProperties(User source, User target) {
        target.setId(source.getId());
        target.setUsername(source.getUsername());
        target.setNombre(source.getNombre());
        target.setApellido(source.getApellido());
        target.setDni(source.getDni());
        target.setTelefono(source.getTelefono());
        target.setEmail(source.getEmail());
        target.setFechaCreacion(source.getFechaCreacion());
        target.setFechaCese(source.getFechaCese());
        target.setEstado(source.getEstado());
        target.setRole(source.getRole());
        target.setSede(source.getSede());
        target.setSedeNombre(source.getSedeNombre());
        target.setToken(source.getToken());
    }

    @Override
    public ResponseEntity<GenericResponse<User>> getUsuarioById(Long userId) {
        try {
            User usuario = findUserById(userId);
            if (usuario != null) {
                // Ya estamos devolviendo una copia segura desde findUserById
                return ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "Usuario encontrado exitosamente",
                                usuario));
            } else {
                throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
            }
        } catch (Exception e) {
            if (!(e instanceof MidasExceptions)) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }
            throw e;
        }
    }

    @Override
    public ResponseEntity<GenericResponse<Void>> deleteUserById(Long userId) {
        try {
            if (!userRepository.existsById(userId)) {
                throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
            }

            // Verificar si el usuario tiene asesores asignados (si es coordinador)
            User user = userRepository.findById(userId).orElse(null);
            if (user != null && user.getRole() == Role.COORDINADOR) {
                List<User> asesores = userRepository.findByCoordinadorId(userId);
                if (!asesores.isEmpty()) {
                    throw new MidasExceptions(MidasErrorMessage.USUARIO_INVALID_DATA,
                            "No se puede eliminar el coordinador porque tiene asesores asignados");
                }
            }

            // Verificar el estado del usuario
            if (user != null) {
                // Si el usuario ya está inactivo, eliminarlo definitivamente
                if ("I".equals(user.getEstado())) {
                    userRepository.deleteById(userId);
                    log.info("Usuario con ID {} eliminado definitivamente", userId);
                    return ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Usuario eliminado definitivamente",
                                    null));
                }
                // Si el usuario está activo, marcarlo como inactivo
                else if ("A".equals(user.getEstado())) {
                    user.setEstado("I");
                    user.setDeletionTime(LocalDateTime.now());
                    userRepository.save(user);
                    log.info("Usuario con ID {} marcado como inactivo", userId);
                    return ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Usuario marcado como inactivo",
                                    null));
                }
            }

            // Si llegamos aquí, algo salió mal
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "No se pudo procesar la eliminación del usuario",
                            null));
        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            log.error("Error al eliminar usuario: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public ResponseEntity<GenericResponse<Void>> deleteUserById(DeleteUserDTO deleteUserDTO) {
        try {
            if (!userRepository.existsById(deleteUserDTO.getUserId())) {
                throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
            }

            // Verificar si el usuario tiene asesores asignados (si es coordinador)
            User user = userRepository.findById(deleteUserDTO.getUserId()).orElse(null);
            if (user != null && user.getRole() == Role.COORDINADOR) {
                List<User> asesores = userRepository.findByCoordinadorId(deleteUserDTO.getUserId());
                if (!asesores.isEmpty()) {
                    throw new MidasExceptions(MidasErrorMessage.USUARIO_INVALID_DATA,
                            "No se puede eliminar el coordinador porque tiene asesores asignados");
                }
            }

            // Verificar el estado del usuario y la solicitud de eliminación permanente
            if (user != null) {
                // Si se solicita eliminación permanente y el usuario ya está inactivo,
                // eliminarlo definitivamente
                if (deleteUserDTO.isPermanent() && "I".equals(user.getEstado())) {
                    userRepository.deleteById(deleteUserDTO.getUserId());
                    log.info("Usuario con ID {} eliminado definitivamente", deleteUserDTO.getUserId());
                    return ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Usuario eliminado definitivamente",
                                    null));
                }
                // Si no se solicita eliminación permanente o el usuario está activo, marcarlo
                // como inactivo
                else if (!deleteUserDTO.isPermanent() || "A".equals(user.getEstado())) {
                    user.setEstado("I");
                    user.setDeletionTime(LocalDateTime.now());
                    userRepository.save(user);
                    log.info("Usuario con ID {} marcado como inactivo", deleteUserDTO.getUserId());
                    return ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Usuario marcado como inactivo",
                                    null));
                }
            }

            // Si llegamos aquí, algo salió mal
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "No se pudo procesar la eliminación del usuario",
                            null));
        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            log.error("Error al eliminar usuario: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(readOnly = true)
    public ResponseEntity<GenericResponse<Map<String, Object>>> searchUsers(String query, int page, int size,
            Long sedeId) {
        try {
            Pageable paging = PageRequest.of(page, size);
            Page<User> pageUsers;

            // Si se proporciona un ID de sede, filtrar por sede
            if (sedeId != null) {
                log.info("Buscando usuarios con query '{}' y sede_id: {}", query, sedeId);
                pageUsers = searchAllFieldsBySede(query, sedeId, paging);
            } else {
                log.info("Buscando usuarios con query '{}'", query);
                pageUsers = searchAllFields(query, paging);
            }

            // Obtener IDs de los usuarios para cargar relaciones en una sola consulta
            List<Long> userIds = pageUsers.getContent().stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            if (!userIds.isEmpty()) {
                // Cargar usuarios con sus relaciones en una sola consulta
                List<User> usersWithRelations = userRepository.findAllWithSedeAndCoordinadorByIds(userIds);

                // Mapear por ID para mantener el orden original
                Map<Long, User> userMap = usersWithRelations.stream()
                        .collect(Collectors.toMap(User::getId, user -> user));

                // Reconstruir la lista en el orden original
                List<User> orderedUsers = userIds.stream()
                        .map(userMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // Convertir a DTOs
                List<UserDTO> userDTOs = orderedUsers.stream()
                        .map(UserMapper::toDTO)
                        .collect(Collectors.toList());

                Map<String, Object> response = new HashMap<>();
                response.put("users", userDTOs);
                response.put("currentPage", pageUsers.getNumber());
                response.put("totalItems", pageUsers.getTotalElements());
                response.put("totalPages", pageUsers.getTotalPages());

                return ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "Búsqueda de usuarios completada exitosamente",
                                response));
            } else {
                // Si no hay resultados, devolver una respuesta vacía
                Map<String, Object> response = new HashMap<>();
                response.put("users", new ArrayList<>());
                response.put("currentPage", pageUsers.getNumber());
                response.put("totalItems", 0L);
                response.put("totalPages", 0);

                return ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "No se encontraron usuarios con los criterios de búsqueda",
                                response));
            }
        } catch (Exception e) {
            log.error("Error al buscar usuarios: {}", e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<User>> updateUserById(Long id, User user) {
        try {
            User existingUser = userRepository.findById(id)
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

            // Validar campos obligatorios
            List<String> validationErrors = UserValidation.validateUser(user);
            if (!validationErrors.isEmpty()) {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                MidasErrorMessage.USUARIO_INVALID_DATA.getErrorMessage(),
                                existingUser));
            }

            // Validar unicidad de username y DNI
            if (user.getUsername() != null && !user.getUsername().equals(existingUser.getUsername())) {
                if (userRepository.existsByUsername(user.getUsername())) {
                    return ResponseEntity.badRequest().body(
                            new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    MidasErrorMessage.USUARIO_USERNAME_EXISTS.getErrorMessage(),
                                    existingUser));
                }
            }

            if (user.getDni() != null && !user.getDni().equals(existingUser.getDni())) {
                if (userRepository.existsByDni(user.getDni())) {
                    return ResponseEntity.badRequest().body(
                            new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    MidasErrorMessage.USUARIO_DNI_EXISTS.getErrorMessage(),
                                    existingUser));
                }
            }

            // Actualizar solo los campos proporcionados
            if (user.getUsername() != null)
                existingUser.setUsername(user.getUsername());
            if (user.getPassword() != null)
                existingUser.setPassword(user.getPassword());
            if (user.getNombre() != null)
                existingUser.setNombre(user.getNombre());
            if (user.getApellido() != null)
                existingUser.setApellido(user.getApellido());
            if (user.getDni() != null)
                existingUser.setDni(user.getDni());
            if (user.getEmail() != null)
                existingUser.setEmail(user.getEmail());
            if (user.getTelefono() != null)
                existingUser.setTelefono(user.getTelefono());
            if (user.getSede() != null)
                existingUser.setSede(user.getSede());
            if (user.getRole() != null)
                existingUser.setRole(user.getRole());

            User updatedUser = userRepository.save(existingUser);
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Usuario actualizado exitosamente",
                            updatedUser));
        } catch (MidasExceptions e) {
            return ResponseEntity.badRequest().body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            e.getMessage(),
                            null));
        } catch (Exception e) {
            log.error("Error al actualizar usuario: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            MidasErrorMessage.ERROR_INTERNAL.getErrorMessage(),
                            null));
        }
    }

    @Override
    @Transactional
    public User createUser(User user) {

        /* ── 1. Validaciones de unicidad ─────────────────────────────────── */
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_USERNAME_EXISTS);
        }
        if (userRepository.existsByDni(user.getDni())) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_DNI_EXISTS);
        }

        /* ── 2. Validar y resolver la sede (si viene un id) ──────────────── */
        if (user.getSede() != null && user.getSede().getId() != null) {
            Long sedeId = user.getSede().getId();
            Sede sedeReal = sedeRepository.findById(sedeId)
                    .orElseThrow(() -> new MidasExceptions(
                            MidasErrorMessage.SEDE_NOT_FOUND,
                            "La sede con id " + sedeId + " no existe"));

            user.setSede(sedeReal); // relación JPA
            user.setSedeNombre(sedeReal.getNombre()); // backup anti-lazy
        } else {
            throw new MidasExceptions(
                    MidasErrorMessage.USUARIO_INVALID_DATA,
                    "La sede es obligatoria");
        }

        /* ── 3. Preparar campos automáticos ──────────────────────────────── */
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setRole(user.getRole() != null ? user.getRole() : Role.ASESOR);
        user.setEstado("A");
        user.setFechaCreacion(LocalDateTime.now());

        if (user.getEmail() == null || user.getEmail().isBlank()) {
            user.setEmail(user.getUsername() + "@midas.pe");
        }

        /* ── 4. Guardar y devolver ───────────────────────────────────────── */
        return userRepository.save(user);
    }

    @Override
    public List<UserDTO> getUsersByRole(Role role) {
        try {
            log.info("Obteniendo usuarios por rol: {}", role);
            List<User> users = userRepository.findByRoleAndEstado(role, "A");
            return users.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener usuarios por rol {}: {}", role, e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public List<UserDTO> getUsersBySede(Long sedeId) {
        try {
            log.info("Obteniendo usuarios por sede: {}", sedeId);
            List<User> users = userRepository.findBySedeIdAndEstado(sedeId, "A");
            return users.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener usuarios por sede {}: {}", sedeId, e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    @Override
    public List<UserDTO> getUsersBySedeAndRole(Long sedeId, Role role) {
        try {
            log.info("Obteniendo usuarios por sede {} y rol: {}", sedeId, role);
            List<User> users = userRepository.findBySedeIdAndRoleAndEstado(sedeId, role, "A");
            return users.stream()
                    .map(UserMapper::toDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error al obtener usuarios por sede {} y rol {}: {}", sedeId, role, e.getMessage(), e);
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

}
