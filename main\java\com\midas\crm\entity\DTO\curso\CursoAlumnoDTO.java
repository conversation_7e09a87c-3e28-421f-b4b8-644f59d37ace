package com.midas.crm.entity.DTO.curso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO optimizado para mostrar alumnos de un curso
 * Solo incluye los campos necesarios para la vista
 * Mantiene compatibilidad con el frontend existente
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoAlumnoDTO {

    // ID de la asignación (para compatibilidad con frontend)
    private Long id;

    // Información de la asignación
    private LocalDateTime fechaAsignacion;
    private String estado;
    private boolean completado;
    private LocalDateTime fechaCompletado;
    private int porcentajeCompletado;
    private LocalDateTime ultimaVisualizacion;

    // Usuario optimizado (solo campos necesarios)
    private UsuarioDTO usuario;

    // Información de progreso
    private ProgresoDTO progreso;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsuarioDTO {
        private Long id;
        private String username;
        private String nombre;
        private String apellido;
        private String dni;
        private String email;
        private String role;
        // Eliminamos: password, fechaCreacion, fechaCese, deletionTime, etc.
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgresoDTO {
        private int totalLecciones;
        private int leccionesCompletadas;
        private int porcentajeProgreso;
    }
}
