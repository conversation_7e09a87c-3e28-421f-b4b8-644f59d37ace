-- Migración para agregar los campos textoTranscription, urlDriveTranscripcion y notaAgenteComparadorIA a la tabla cliente_residencial
-- Fecha: 2024
-- Descripción: Agrega tres nuevos campos para almacenar transcripciones de texto, URLs de Drive y notas del agente comparador IA

-- Agregar columna para texto de transcripción (tipo TEXT para almacenar mucho contenido)
ALTER TABLE cliente_residencial 
ADD COLUMN texto_transcription TEXT NULL 
COMMENT 'Campo para almacenar transcripciones de texto de gran tamaño';

-- Agregar columna para URL de Drive de transcripción
ALTER TABLE cliente_residencial
ADD COLUMN url_drive_transcripcion VARCHAR(500) NULL
COMMENT 'Campo para almacenar URLs de Google Drive con transcripciones';

-- Agregar columna para notas del agente comparador IA (valores numéricos)
ALTER TABLE cliente_residencial
ADD COLUMN nota_agente_comparador_ia DECIMAL(5,2) NULL
COMMENT 'Campo para almacenar valores numéricos del agente comparador de IA (ej: 8.75, 9.50)';

-- Verificar que las columnas se agregaron correctamente
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_NAME = 'cliente_residencial' 
-- AND COLUMN_NAME IN ('texto_transcription', 'url_drive_transcripcion', 'nota_agente_comparador_ia');
