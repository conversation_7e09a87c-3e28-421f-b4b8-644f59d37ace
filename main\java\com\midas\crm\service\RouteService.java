package com.midas.crm.service;

import com.midas.crm.entity.DTO.route.RouteCreateDTO;
import com.midas.crm.entity.DTO.route.RouteDTO;
import com.midas.crm.entity.DTO.route.RoutePageDTO;
import com.midas.crm.entity.DTO.route.RouteUpdateDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.Route;
import org.springframework.http.ResponseEntity;
import com.midas.crm.utils.GenericResponse;

import java.util.List;

public interface RouteService {

    ResponseEntity<GenericResponse<RoutePageDTO>> listRoutes(int page, int size, boolean activeOnly);

    ResponseEntity<GenericResponse<RoutePageDTO>> searchRoutes(String query, int page, int size, boolean activeOnly);

    ResponseEntity<GenericResponse<RouteDTO>> createRoute(RouteCreateDTO createDTO);

    ResponseEntity<GenericResponse<RouteDTO>> updateRoute(Long id, RouteUpdateDTO updateDTO);

    ResponseEntity<GenericResponse<Void>> deleteRoute(Long id);

    ResponseEntity<GenericResponse<RouteDTO>> getRouteById(Long id);

    ResponseEntity<GenericResponse<RouteDTO>> getRouteByPath(String path);

    ResponseEntity<GenericResponse<List<RouteDTO>>> getRoutesByRole(Role role);

    ResponseEntity<GenericResponse<Void>> assignRoleToRoute(Long routeId, Role role, Boolean canAccess);

    ResponseEntity<GenericResponse<Void>> removeRoleFromRoute(Long routeId, Role role);

    ResponseEntity<GenericResponse<List<Role>>> getRolesByRoute(Long routeId);

    Route findRouteById(Long id);

    Route findRouteByPath(String path);

    boolean existsByPath(String path);
}
