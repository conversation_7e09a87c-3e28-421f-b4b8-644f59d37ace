package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.role.RoleDTO;
import com.midas.crm.entity.DTO.role.RoleRouteDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.RoleRoute;
import com.midas.crm.entity.Route;
import com.midas.crm.entity.User;
import com.midas.crm.repository.RoleRouteRepository;
import com.midas.crm.repository.RouteRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.RoleService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.ResponseBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleRouteRepository roleRouteRepository;
    private final RouteRepository routeRepository;
    private final UserRepository userRepository;

    @Override
    public ResponseEntity<GenericResponse<List<RoleDTO>>> getAllRoles() {
        try {
            List<RoleDTO> roleDTOs = Arrays.stream(Role.values())
                    .map(role -> {
                        RoleDTO dto = new RoleDTO(role);
                        // Contar usuarios con este rol
                        long userCount = userRepository.countByRole(role);
                        dto.setUserCount((int) userCount);
                        return dto;
                    })
                    .collect(Collectors.toList());

            return ResponseBuilder.success(roleDTOs, "Roles obtenidos exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener roles: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener roles: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<RoleDTO>> getRoleInfo(Role role) {
        try {
            RoleDTO dto = new RoleDTO(role);
            long userCount = userRepository.countByRole(role);
            dto.setUserCount((int) userCount);

            return ResponseBuilder.success(dto, "Información del rol obtenida exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener información del rol: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener información del rol: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<List<RoleRouteDTO>>> getRoleRoutes(Role role) {
        try {
            List<RoleRoute> roleRoutes = roleRouteRepository.findByRole(role);
            List<RoleRouteDTO> roleDTOs = roleRoutes.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return ResponseBuilder.success(roleDTOs, "Rutas del rol obtenidas exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener rutas del rol: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener rutas del rol: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<Void>> updateRoleRoutes(Role role, List<Long> routeIds) {
        try {
            // Eliminar todas las rutas existentes para este rol
            List<RoleRoute> existingRoleRoutes = roleRouteRepository.findByRole(role);
            roleRouteRepository.deleteAll(existingRoleRoutes);

            // Agregar las nuevas rutas
            for (Long routeId : routeIds) {
                Route route = routeRepository.findById(routeId).orElse(null);
                if (route != null) {
                    RoleRoute roleRoute = new RoleRoute();
                    roleRoute.setRole(role);
                    roleRoute.setRoute(route);
                    roleRoute.setCanAccess(true);
                    roleRouteRepository.save(roleRoute);
                }
            }

            return ResponseBuilder.success(null, "Rutas del rol actualizadas exitosamente");
        } catch (Exception e) {
            log.error("Error al actualizar rutas del rol: {}", e.getMessage());
            return ResponseBuilder.error("Error al actualizar rutas del rol: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<List<Role>>> getRolesByRoutePath(String path) {
        try {
            List<Role> roles = roleRouteRepository.findRolesByRoutePath(path);
            return ResponseBuilder.success(roles, "Roles obtenidos exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener roles por ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener roles por ruta: " + e.getMessage());
        }
    }

    @Override
    public boolean hasRoleAccessToRoute(Role role, String path) {
        try {
            List<Role> allowedRoles = roleRouteRepository.findRolesByRoutePath(path);
            return allowedRoles.contains(role);
        } catch (Exception e) {
            log.error("Error al verificar acceso del rol a la ruta: {}", e.getMessage());
            return false;
        }
    }

    private RoleRouteDTO convertToDTO(RoleRoute roleRoute) {
        RoleRouteDTO dto = new RoleRouteDTO();
        dto.setId(roleRoute.getId());
        dto.setRole(roleRoute.getRole());
        dto.setRouteId(roleRoute.getRoute().getId());
        dto.setRoutePath(roleRoute.getRoute().getPath());
        dto.setRouteName(roleRoute.getRoute().getName());
        dto.setCanAccess(roleRoute.getCanAccess());
        dto.setCreatedAt(roleRoute.getCreatedAt());
        dto.setUpdatedAt(roleRoute.getUpdatedAt());
        return dto;
    }
}
