package com.midas.crm.service;

import com.google.api.client.auth.oauth2.AuthorizationCodeFlow;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.auth.oauth2.TokenResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.drive.DriveScopes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;
import java.util.Collections;

/**
 * Servicio para manejar OAuth2 de Google Drive en producción
 */
@Service
@Slf4j
public class GoogleOAuthService {

    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    
    @Value("${google.drive.credentials.file:client_secret.json}")
    private String credentialsFilePath;
    
    @Value("${google.drive.oauth.redirect-uri}")
    private String redirectUri;
    
    @Value("${google.drive.oauth.tokens.file:tokens.json}")
    private String tokensFilePath;

    private AuthorizationCodeFlow flow;

    /**
     * Inicializa el flujo de autorización OAuth2
     */
    private AuthorizationCodeFlow getFlow() throws IOException, GeneralSecurityException {
        if (flow == null) {
            NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            
            // Cargar client secrets
            InputStream in = getCredentialsInputStream();
            GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));
            
            // Crear el flujo de autorización
            flow = new GoogleAuthorizationCodeFlow.Builder(
                    httpTransport, JSON_FACTORY, clientSecrets, Collections.singletonList(DriveScopes.DRIVE))
                    .setDataStoreFactory(new FileDataStoreFactory(new File(tokensFilePath)))
                    .setAccessType("offline")
                    .build();
        }
        return flow;
    }

    /**
     * Genera la URL de autorización para redirigir al usuario
     */
    public String getAuthorizationUrl() throws IOException, GeneralSecurityException {
        AuthorizationCodeFlow flow = getFlow();
        
        String authorizationUrl = flow.newAuthorizationUrl()
                .setRedirectUri(redirectUri)
                .setState("oauth_state_" + System.currentTimeMillis()) // Estado para seguridad
                .build();
        
        log.info("URL de autorización generada: {}", authorizationUrl);
        return authorizationUrl;
    }

    /**
     * Procesa el código de autorización recibido del callback
     */
    public Credential processAuthorizationCode(String authorizationCode) throws IOException, GeneralSecurityException {
        AuthorizationCodeFlow flow = getFlow();

        // Intercambiar el código por tokens
        TokenResponse tokenResponse = flow.newTokenRequest(authorizationCode)
                .setRedirectUri(redirectUri)
                .execute();

        // Crear credencial desde la respuesta del token
        Credential credential = flow.createAndStoreCredential(tokenResponse, "user");

        log.info("Credenciales OAuth obtenidas y guardadas exitosamente");
        return credential;
    }

    /**
     * Obtiene las credenciales guardadas (si existen)
     */
    public Credential getStoredCredential() throws IOException, GeneralSecurityException {
        AuthorizationCodeFlow flow = getFlow();
        return flow.loadCredential("user");
    }

    /**
     * Verifica si hay credenciales válidas guardadas
     */
    public boolean hasValidCredentials() {
        try {
            Credential credential = getStoredCredential();
            return credential != null && (credential.getAccessToken() != null || credential.getRefreshToken() != null);
        } catch (Exception e) {
            log.warn("Error verificando credenciales: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Revoca las credenciales actuales
     */
    public void revokeCredentials() throws IOException, GeneralSecurityException {
        Credential credential = getStoredCredential();
        if (credential != null) {
            // Eliminar las credenciales del almacén
            getFlow().getCredentialDataStore().delete("user");
            log.info("Credenciales eliminadas exitosamente");
        }
    }

    /**
     * Carga el archivo de credenciales desde classpath
     */
    private InputStream getCredentialsInputStream() throws IOException {
        try {
            ClassPathResource resource = new ClassPathResource(credentialsFilePath);
            if (resource.exists()) {
                log.info("Cargando credenciales desde classpath: {}", credentialsFilePath);
                return resource.getInputStream();
            }
            throw new IOException("Archivo de credenciales no encontrado: " + credentialsFilePath);
        } catch (Exception e) {
            log.error("Error al cargar credenciales: {}", e.getMessage());
            throw new IOException("No se pudo cargar el archivo de credenciales: " + credentialsFilePath, e);
        }
    }
}
