package com.midas.crm.service;

import com.midas.crm.entity.DTO.role.RoleDTO;
import com.midas.crm.entity.DTO.role.RoleRouteDTO;
import com.midas.crm.entity.Role;
import org.springframework.http.ResponseEntity;
import com.midas.crm.utils.GenericResponse;

import java.util.List;

public interface RoleService {

    ResponseEntity<GenericResponse<List<RoleDTO>>> getAllRoles();

    ResponseEntity<GenericResponse<RoleDTO>> getRoleInfo(Role role);

    ResponseEntity<GenericResponse<List<RoleRouteDTO>>> getRoleRoutes(Role role);

    ResponseEntity<GenericResponse<Void>> updateRoleRoutes(Role role, List<Long> routeIds);

    ResponseEntity<GenericResponse<List<Role>>> getRolesByRoutePath(String path);

    boolean hasRoleAccessToRoute(Role role, String path);
}
